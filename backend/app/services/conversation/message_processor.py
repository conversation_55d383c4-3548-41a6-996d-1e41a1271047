"""
消息处理模块

提供处理消息的共享核心逻辑、提示词构建和消息格式化功能

函数与关键变量说明:
---------------------------------------

消息处理核心:
* process_message_common - 处理消息的共享核心逻辑，构建提示词
  - conversation_id: 会话ID
  - content: 消息内容
  - target_agent_id: 目标智能体ID(可选)
  - user_id: 用户ID(可选)
  - target_agent_ids: 目标智能体ID列表(可选，多个智能体，优先级高于target_agent_id)
  - no_new_message: 是否跳过创建新消息 (可选，用于流式API中复用已创建的消息)
  - existing_human_message: 已存在的人类消息对象 (可选，与no_new_message配合使用)
  - 返回: (human_message, agent, agent_role, role_model, model_messages, conversation, role_model_params, agent_info, model_settings)

提示词构建:
* build_system_prompt - 构建系统提示词
  - agent: 智能体对象
  - agent_role: 智能体角色对象
  - action_task: 行动任务对象
  - conversation: 会话对象
  - tool_definitions: 工具定义列表
  - tool_names: 工具名称列表
  - role_capabilities: 角色能力列表

消息格式化:
* format_messages - 格式化消息为模型可用的格式
  - system_prompt: 系统提示词
  - recent_messages: 最近的消息列表
  - content: 当前消息内容
"""
import json
import logging
import os
import re
from typing import List, Dict, Any, Optional, Tuple

from app.models import (
    db, Conversation, Message, Agent, ActionTask, RuleSet, Rule,
    ActionSpace, Role, RoleCapability, Capability, RoleTool, Tool,
    ActionTaskEnvironmentVariable, ActionSpaceRole, ActionTaskAgent,
    RoleKnowledge, Knowledge, RoleExternalKnowledge, ExternalKnowledge, ExternalKnowledgeProvider
)
from app.utils.datetime_utils import get_current_time_with_timezone
from app.services.mcp_server_manager import mcp_manager
from app.services.tool_schema_cache import tool_schema_cache
from app.services.workspace_service import WorkspaceService

logger = logging.getLogger(__name__)

def process_message_common(conversation_id: int, content: str, target_agent_id=None, user_id=None,
                          target_agent_ids=None, no_new_message=False, existing_human_message=None, send_target=None) -> tuple:
    """
    处理消息的共享核心逻辑

    Args:
        conversation_id: 会话ID
        content: 消息内容
        target_agent_id: 目标智能体ID (可选，单个智能体)
        user_id: 用户ID (可选)
        target_agent_ids: 目标智能体ID列表 (可选，多个智能体，优先级高于target_agent_id)
        no_new_message: 是否跳过创建新消息 (可选，用于流式API中复用已创建的消息)
        existing_human_message: 已存在的人类消息对象 (可选，与no_new_message配合使用)
        send_target: 发送目标 (可选，'supervisor'表示发送给监督者，'task'表示发送给任务会话，'task_intervention'表示监督者干预)

    Returns:
        tuple: (human_message, target_agent, role, model_config, formatted_messages, conversation, role_model_params, agent_info, model_settings)
    """
    # 如果提供了target_agent_ids列表，优先使用它
    if target_agent_ids and len(target_agent_ids) > 0:
        if len(target_agent_ids) == 1:
            # 如果列表只有一个元素，使用单个智能体处理逻辑
            target_agent_id = target_agent_ids[0]
        else:
            # 多个智能体时，只处理第一个智能体的响应
            # 注意：多个智能体的处理应在流式API中完成，这里只处理普通API的情况
            logger.info(f"process_message_common收到多个智能体ID: {target_agent_ids}，将只处理第一个")
            target_agent_id = target_agent_ids[0]

    # 检查会话是否存在
    conversation = Conversation.query.get(conversation_id)
    if not conversation:
        logger.warning(f"会话不存在: {conversation_id}")
        return None, None, None, None, None, None, None, None, None

    if conversation.status != 'active':
        logger.warning(f"会话状态为{conversation.status}，不能添加消息")
        return None, None, None, None, None, None, None, None, None

    # 创建人类消息 - 如果no_new_message为True则使用existing_human_message
    human_message = existing_human_message
    if not no_new_message:
        human_message = Message(
            content=content,
            role='human',
            user_id=user_id,
            action_task_id=conversation.action_task_id,
            conversation_id=conversation_id
        )

        # 根据send_target设置source字段、agent_id和meta字段
        logger.debug(f"[消息处理器] 检查发送目标: send_target={send_target}, target_agent_id={target_agent_id}")
        if send_target == 'supervisor' and target_agent_id:
            human_message.agent_id = target_agent_id
            human_message.source = 'supervisorConversation'
            logger.debug(f"[消息处理器] 用户消息发送给监督者，设置agent_id={target_agent_id}, source=supervisorConversation")
        elif send_target == 'task_intervention' and target_agent_id:
            # 监督者干预：source设为supervisorConversation，设置meta.type为info
            human_message.agent_id = target_agent_id
            human_message.source = 'supervisorConversation'
            # 设置干预meta信息
            if not human_message.meta:
                human_message.meta = {}
            human_message.meta['type'] = 'info'
            logger.debug(f"[消息处理器] 监督者干预消息，设置agent_id={target_agent_id}, source=supervisorConversation, meta.type=info")
        else:
            human_message.source = 'taskConversation'
            logger.debug(f"[消息处理器] 用户消息发送给任务会话，设置source=taskConversation，原因：send_target={send_target}, target_agent_id={target_agent_id}")

        # 存储目标智能体ID信息到meta字段
        if not human_message.meta:
            human_message.meta = {}

        if target_agent_ids:
            human_message.meta['target_agent_ids'] = target_agent_ids
            logger.info(f"[消息处理器] 用户消息指定多个目标智能体ID: {target_agent_ids}")
        elif target_agent_id:
            human_message.meta['target_agent_ids'] = [target_agent_id]
            logger.info(f"[消息处理器] 用户消息指定单个目标智能体ID: {target_agent_id}")
        else:
            # 如果没有指定目标智能体，则表示发送给所有智能体
            logger.info(f"[消息处理器] 用户消息未指定目标智能体，将发送给所有智能体")

        db.session.add(human_message)

        # 更新行动任务的updated_at时间
        if conversation.action_task_id:
            from app.utils.datetime_utils import get_current_time_with_timezone
            action_task = ActionTask.query.get(conversation.action_task_id)
            if action_task:
                action_task.updated_at = get_current_time_with_timezone()
                logger.debug(f"更新行动任务的updated_at时间: 任务ID={conversation.action_task_id}")

        db.session.commit()

        logger.info(f"创建了人类消息 ID={human_message.id}")

    # 获取目标智能体
    agent = None
    action_task = ActionTask.query.get(conversation.action_task_id)
    if not action_task:
        logger.warning(f"未找到关联的行动任务，无法生成回复，会话ID: {conversation_id}")
        return human_message, None, None, None, None, conversation, None, None, None

    if target_agent_id:
        # 检查目标智能体是否属于此行动任务
        agent_relation = ActionTaskAgent.query.filter_by(
            action_task_id=action_task.id,
            agent_id=target_agent_id
        ).first()

        if agent_relation:
            agent = Agent.query.get(agent_relation.agent_id)
            logger.info(f"从行动任务中获取到目标智能体: {agent.name if agent else 'None'}")
        else:
            logger.warning(f"目标智能体 {target_agent_id} 不属于行动任务 {action_task.id}")
    else:
        # 获取默认智能体
        agent_relation = ActionTaskAgent.query.filter_by(
            action_task_id=action_task.id,
            is_default=True
        ).first()

        if agent_relation:
            agent = Agent.query.get(agent_relation.agent_id)
            logger.info(f"从行动任务中获取到默认智能体: {agent.name if agent else 'None'}")
        else:
            # 如果没有默认智能体，尝试获取行动任务中的第一个智能体
            agent_relation = ActionTaskAgent.query.filter_by(
                action_task_id=action_task.id
            ).first()

            if agent_relation:
                agent = Agent.query.get(agent_relation.agent_id)
                logger.info(f"从行动任务中获取到第一个智能体: {agent.name if agent else 'None'}")

    if not agent:
        logger.warning(f"未找到智能体，无法生成回复，会话ID: {conversation_id}")
        return human_message, None, None, None, None, conversation, None, None, None

    # 获取智能体的角色及其模型配置
    agent_role = None
    role_model = None
    model_settings = {}

    # 从智能体获取角色
    if hasattr(agent, 'role_id') and agent.role_id:
        agent_role = Role.query.get(agent.role_id)
        logger.info(f"从智能体 {agent.id} 获取到角色: {agent_role.name if agent_role else 'None'}")

    # 检查是否为外部角色
    is_external_role = agent_role and agent_role.source == 'external'

    # 从角色获取模型信息
    if is_external_role:
        # 外部角色：从settings.external_config获取配置
        logger.info(f"检测到外部角色: {agent_role.name}")

        if agent_role.settings and 'external_config' in agent_role.settings:
            external_config = agent_role.settings['external_config']
            api_config = external_config.get('api_config', {})
            platform_specific = external_config.get('platform_specific', {})

            # 构建外部角色的模型配置
            # 外部角色的max_tokens从external_config中获取，如果没有则使用默认值
            external_max_tokens = external_config.get('max_tokens', 2000)
            model_settings = {
                'model_id': api_config.get('model', 'external-model'),
                'temperature': agent_role.temperature if agent_role.temperature is not None else 0.7,
                'max_tokens': external_max_tokens,
                'api_url': api_config.get('base_url', ''),
                'api_key': api_config.get('api_key', ''),
                'top_p': agent_role.top_p if agent_role.top_p is not None else 1.0,
                'frequency_penalty': agent_role.frequency_penalty if agent_role.frequency_penalty is not None else 0.0,
                'presence_penalty': agent_role.presence_penalty if agent_role.presence_penalty is not None else 0.0,
                'stop_sequences': agent_role.stop_sequences if agent_role.stop_sequences is not None else [],
                # 外部角色特有配置
                'platform': external_config.get('platform', 'custom'),
                'external_id': external_config.get('external_id', ''),
                'platform_specific': platform_specific
            }

            # 创建一个虚拟的role_model对象用于兼容性
            class ExternalRoleModel:
                def __init__(self, config):
                    self.model_id = config.get('model_id')
                    self.base_url = config.get('api_url')
                    self.api_key = config.get('api_key')
                    self.provider = config.get('platform', 'external')
                    self.temperature = config.get('temperature', 0.7)
                    self.max_output_tokens = config.get('max_tokens', 2000)

            role_model = ExternalRoleModel(model_settings)
            logger.info(f"从外部角色 {agent_role.id} 获取到配置: platform={model_settings.get('platform')}, api_url={model_settings.get('api_url')}")
        else:
            logger.warning(f"外部角色 {agent_role.name} 缺少external_config配置")
            return human_message, agent, agent_role, None, None, conversation, None, None, None
    else:
        # 内部角色：使用原有逻辑
        if agent_role and hasattr(agent_role, 'model') and agent_role.model:
            # 获取模型的配置
            from app.models import ModelConfig
            role_model = ModelConfig.query.get(agent_role.model)
            if role_model:
                # 获取模型配置，为角色参数设置默认值
                model_settings = {
                    'model_id': role_model.model_id,
                    'temperature': agent_role.temperature if agent_role.temperature is not None else 0.7,
                    'max_tokens': role_model.max_output_tokens or 2000,  # max_tokens从模型配置中获取
                    'api_url': role_model.base_url,
                    'api_key': role_model.api_key,
                    'top_p': agent_role.top_p if agent_role.top_p is not None else 1.0,
                    'frequency_penalty': agent_role.frequency_penalty if agent_role.frequency_penalty is not None else 0.0,
                    'presence_penalty': agent_role.presence_penalty if agent_role.presence_penalty is not None else 0.0,
                    'stop_sequences': agent_role.stop_sequences if agent_role.stop_sequences is not None else []
                }
                logger.info(f"从内部角色 {agent_role.id} 获取到模型配置: {model_settings}")

        if not role_model:
            # 尝试使用默认文本生成模型
            from app.models import ModelConfig
            role_model = ModelConfig.query.filter_by(is_default_text=True).first()
            if not role_model:
                # 如果没有设置默认文本生成模型，查找第一个支持文本输出的模型
                text_models = ModelConfig.query.filter(
                    ModelConfig.modalities.contains('text_output')
                ).all()
                if text_models:
                    role_model = text_models[0]
            if role_model:
                # 使用默认模型配置，但仍然从角色获取模型参数（如果有的话）
                model_settings = {
                    'model_id': role_model.model_id,
                    'temperature': agent_role.temperature if agent_role and agent_role.temperature is not None else 0.7,
                    'max_tokens': role_model.max_output_tokens or 2000,  # 从模型配置中获取
                    'api_url': role_model.base_url,
                    'api_key': role_model.api_key,
                    'top_p': agent_role.top_p if agent_role and agent_role.top_p is not None else 1.0,
                    'frequency_penalty': agent_role.frequency_penalty if agent_role and agent_role.frequency_penalty is not None else 0.0,
                    'presence_penalty': agent_role.presence_penalty if agent_role and agent_role.presence_penalty is not None else 0.0,
                    'stop_sequences': agent_role.stop_sequences if agent_role and agent_role.stop_sequences is not None else []
                }
                logger.info(f"使用默认模型配置: {model_settings}")

        if not role_model:
            logger.warning("找不到可用的模型配置，无法生成回复")
            return human_message, agent, agent_role, None, None, conversation, None, None, None

    # 获取历史消息（根据系统设置中的上下文历史消息长度）
    from app.models import SystemSetting
    max_history_length = SystemSetting.get('max_conversation_history_length', 10)
    logger.info(f"使用系统设置的上下文历史消息长度: {max_history_length}")

    # 构建查询
    query = Message.query.filter(
        Message.conversation_id == conversation_id,
        Message.role.in_(['agent', 'human'])
    ).order_by(Message.created_at.desc())

    # 根据设置决定是否限制数量
    if max_history_length == 0:
        logger.info("上下文历史消息长度设置为0，获取所有历史消息")
    else:
        query = query.limit(max_history_length)

    recent_messages = query.all()

    # 倒序，让最早的消息在前面
    recent_messages.reverse()

    # 开始构建提示词
    # 获取提示模板
    prompt_template = None
    if agent_role and hasattr(agent_role, 'system_prompt') and agent_role.system_prompt:
        prompt_template = agent_role.system_prompt
        logger.info(f"从角色 {agent_role.id} 获取到提示模板")

    # 添加角色工具能力
    role_capabilities = []
    tool_definitions = []  # 用于存储工具定义
    tool_names = []        # 用于存储工具名称

    if agent_role:
        # 查询角色的能力
        role_capability_relations = RoleCapability.query.filter_by(role_id=agent_role.id).all()
        for rc in role_capability_relations:
            capability = Capability.query.get(rc.capability_id)
            if capability:
                role_capabilities.append(capability.name)

                # 只有当角色具备tool_use或function_calling能力时，才继续解析工具
                if capability.tools and ('tool_use' in role_capabilities or 'function_calling' in role_capabilities):
                    # 获取能力关联的工具
                    for server_name, server_tools in capability.tools.items():
                        if isinstance(server_tools, list) and server_tools:
                            try:
                                # 使用全局mcp_manager实例，避免重复创建和加载配置
                                from app.services.mcp_server_manager import mcp_manager

                                # 优先从缓存获取工具定义
                                all_server_tools = None
                                if tool_schema_cache.has_tools(server_name):
                                    logger.debug(f"从缓存获取服务器 {server_name} 的工具定义")
                                    all_server_tools = tool_schema_cache.get_tools(server_name)
                                else:
                                    # 如果缓存中没有，则从服务器获取
                                    logger.debug(f"缓存中没有服务器 {server_name} 的工具定义，从服务器获取")
                                    all_server_tools = mcp_manager.get_tools(server_name)
                                    # 将获取到的工具定义缓存起来
                                    if all_server_tools:
                                        tool_schema_cache.set_tools(server_name, all_server_tools)

                                # 确保工具列表存在
                                tools_list = []
                                if isinstance(all_server_tools, dict) and "tools" in all_server_tools:
                                    tools_list = all_server_tools["tools"]
                                elif isinstance(all_server_tools, list):
                                    tools_list = all_server_tools

                                # 根据角色能力中指定的工具名称筛选
                                for tool in tools_list:
                                    # 获取工具名称
                                    tool_name = None

                                    # 已经是标准格式
                                    if isinstance(tool, dict) and "function" in tool and isinstance(tool["function"], dict) and "name" in tool["function"]:
                                        tool_name = tool["function"]["name"]
                                    # 直接包含name字段
                                    elif isinstance(tool, dict) and "name" in tool:
                                        tool_name = tool.get("name")
                                        # 转换成标准格式
                                        if tool_name and tool_name in server_tools:
                                            # 创建标准格式的工具定义
                                            standardized_tool = {
                                                "type": "function",
                                                "function": {
                                                    "name": tool_name,
                                                    "description": tool.get("description", f"工具: {tool_name}"),
                                                    "parameters": tool.get("parameters", tool.get("inputSchema", {
                                                        "type": "object",
                                                        "properties": {},
                                                        "required": []
                                                    }))
                                                }
                                            }
                                            tool_definitions.append(standardized_tool)
                                            tool_names.append(tool_name)
                                            continue

                                    # 如果工具名在配置的tools列表中且已经是标准格式，直接添加
                                    if tool_name and tool_name in server_tools:
                                        tool_definitions.append(tool)
                                        tool_names.append(tool_name)

                            except Exception as e:
                                logger.error(f"获取服务器 {server_name} 的工具失败: {str(e)}")

    # 构建系统提示词
    system_prompt = build_system_prompt(
        agent=agent,
        agent_role=agent_role,
        action_task=action_task,
        conversation=conversation,
        tool_definitions=tool_definitions,
        tool_names=tool_names,
        role_capabilities=role_capabilities
    )

    # 格式化为模型可用的消息格式
    model_messages = format_messages(system_prompt, recent_messages, content, human_message)

    # 记录将要使用的模型设置
    logger.info(f"使用模型配置: model_id={model_settings.get('model_id')}, temperature={model_settings.get('temperature')}, max_tokens={model_settings.get('max_tokens')}")

    # 准备智能体信息和角色模型参数
    agent_info = {
        'id': agent.id,
        'name': agent.name,
        'role_id': agent_role.id if agent_role else None,
        'role_name': agent_role.name if agent_role else None,
        'capabilities': role_capabilities,
        'tool_names': tool_names,
        'tools': tool_definitions,  # 添加工具定义到agent_info
        'provider': role_model.provider if role_model else 'unknown',  # 添加provider到agent_info
        'is_external': is_external_role,  # 标识是否为外部角色
        'platform': model_settings.get('platform') if is_external_role else None,  # 外部平台类型
        'external_config': {  # 完整的外部配置
            'platform': model_settings.get('platform'),
            'external_id': model_settings.get('external_id'),
            'api_config': {
                'api_key': model_settings.get('api_key'),
                'base_url': model_settings.get('api_url'),
                'model': model_settings.get('model_id')
            },
            'platform_specific': model_settings.get('platform_specific', {})
        } if is_external_role else None
    }

    # 构建角色模型参数，用于传递给LLM API
    role_model_params = {
        'temperature': model_settings.get('temperature', 0.7),
        'max_tokens': model_settings.get('max_tokens', 2000),
        'top_p': model_settings.get('top_p'),
        'frequency_penalty': model_settings.get('frequency_penalty'),
        'presence_penalty': model_settings.get('presence_penalty'),
        'stop': model_settings.get('stop_sequences')  # OpenAI API使用'stop'参数名
    }

    # 添加调试日志，确认参数传递
    logger.info(f"构建的角色模型参数: {role_model_params}")
    logger.info(f"原始模型设置: temperature={model_settings.get('temperature')}, top_p={model_settings.get('top_p')}, frequency_penalty={model_settings.get('frequency_penalty')}, presence_penalty={model_settings.get('presence_penalty')}, stop_sequences={model_settings.get('stop_sequences')}")

    return human_message, agent, agent_role, role_model, model_messages, conversation, role_model_params, agent_info, model_settings

def build_system_prompt(agent, agent_role, action_task, conversation, tool_definitions, tool_names, role_capabilities):
    """构建系统提示词 / Build system prompt

    Args:
        agent: 智能体对象 / Agent object
        agent_role: 智能体角色对象 / Agent role object
        action_task: 行动任务对象 / Action task object
        conversation: 会话对象 / Conversation object
        tool_definitions: 工具定义列表 / Tool definitions list
        tool_names: 工具名称列表 / Tool names list
        role_capabilities: 角色能力列表 / Role capabilities list

    Returns:
        str: 构建好的系统提示词 / Built system prompt
    """
    # 获取提示模板 / Get prompt template
    prompt_template = ""
    if agent_role and hasattr(agent_role, 'system_prompt') and agent_role.system_prompt:
        prompt_template = agent_role.system_prompt

    # 使用f-string进行格式化 / Format using f-string
    # 判断是否为监督者 / Check if this is an observer/supervisor
    is_observer = hasattr(agent, 'is_observer') and agent.is_observer
    observer_role_text = ", and you are a supervisor for this task" if is_observer else ""

    system_prompt = f"""<roleDefinition>
# Role Definition
Your name is {agent.name}, you are a {agent_role.name if agent_role else 'undefined role'}{observer_role_text}, and your ID is {agent.id}.
Please remember your name, role, and ID. You must not refer to yourself in third person or confuse yourself with other agents.

## Role Principles
{prompt_template}

## Additional Principles
*In addition to the above principles, you must also follow the additional role prompts in your action space*
</roleDefinition>"""

    # 如果是监督者，添加监督者特殊提示 / Add special supervisor instructions if this is an observer
    if is_observer:
        system_prompt += """<observerDefinition>
## Supervisor Special Instructions
As a supervisor, your responsibility is to monitor and evaluate the performance of other agents in the action task, rather than directly participating in task execution. You should:
1. Observe whether other agents' behaviors and decisions comply with the rules in the rule set
2. Evaluate whether they follow rules and complete task objectives
3. Provide objective evaluations and suggestions
4. Intervene when necessary, but avoid excessive intervention
5. Maintain a neutral and objective stance
</observerDefinition>

"""

        # 监督者不需要规则检查提示词，因为监督者的职责是监督其他智能体，而不是被监督
        # 这样可以避免监督者回复时触发额外的LLM API调用进行规则检查
        logger.debug(f"跳过为监督者 {agent.name} 添加规则检查结果（监督者不需要被监督）")

    # 获取行动空间信息 / Get action space information
    action_space = None
    if action_task.action_space_id:
        action_space = ActionSpace.query.get(action_task.action_space_id)

    # 格式化其余部分 / Format the rest of the prompt
    system_prompt += f"""<actionSpace>
## Action Space
The action space is the virtual environment where you operate. You play a role in this space and must follow the role principles and rules of this space.

### Action Space Name
{action_space.name if action_space else 'Undefined Action Space'}

### Action Space Description
{action_space.description if action_space else ''}

## Action Space Background
{action_space.settings.get('background', '') if action_space and action_space.settings else ''}

## Action Space Basic Principles
{action_space.settings.get('rules', '') if action_space and action_space.settings else ''}

### Additional Principles You Must Follow in This Action Space
{agent.additional_prompt if hasattr(agent, 'additional_prompt') and agent.additional_prompt else ''}
"""

    task_agents = ActionTaskAgent.query.filter_by(action_task_id=action_task.id).all()

    # 添加当前空间参与角色列表，分为普通参与者和监督者 / Add current space participant role list, separated into regular participants and supervisors
    if task_agents:
        # 获取所有智能体对象 / Get all agent objects
        agent_objects = []
        for task_agent in task_agents:
            agent_obj = Agent.query.get(task_agent.agent_id)
            if agent_obj and agent_obj.role:
                agent_objects.append(agent_obj)

        # 分离普通参与者和监督者 / Separate regular participants and supervisors
        participants = [a for a in agent_objects if not (hasattr(a, 'is_observer') and a.is_observer)]
        observers = [a for a in agent_objects if hasattr(a, 'is_observer') and a.is_observer]

        # 添加普通参与者列表 / Add regular participants list
        if participants:
            system_prompt += "\n## Current Space Participant Role List\n"
            for agent_obj in participants:
                role_name = agent_obj.role.name if hasattr(agent_obj.role, 'name') else ""
                system_prompt += f"- {agent_obj.name}[{role_name}][ID: {agent_obj.id}]\n"

        # 添加监督者列表 / Add supervisors list
        if observers:
            system_prompt += "\n## Current Space Supervisor List\n"
            for agent_obj in observers:
                role_name = agent_obj.role.name if hasattr(agent_obj.role, 'name') else ""
                system_prompt += f"- {agent_obj.name}[{role_name}][ID: {agent_obj.id}]\n"

    system_prompt += f"""<actionTask>
### Current Action Task
Task Name: {action_task.title}
Task ID: {action_task.id}
Task Description: {action_task.description}
"""

# 检查角色是否有环境感知能力 / Check if the role has environment sensing capability
    has_env_sensing = False
    if agent_role:
        # 查询角色的能力 / Query role capabilities
        role_capability_relations = RoleCapability.query.filter_by(role_id=agent_role.id).all()
        for rc in role_capability_relations:
            capability = Capability.query.get(rc.capability_id)
            if capability and capability.name == "environment_sensing":
                has_env_sensing = True
                break

    # 如果角色有环境感知能力，添加环境变量信息 / If the role has environment sensing capability, add environment variable information
    if has_env_sensing:
        # 获取任务的环境变量 / Get task environment variables
        system_prompt += "<agentAndTaskVariables># Variables Related to Task and You\n"
        system_prompt += "## Current Time\n" + get_current_time_with_timezone().strftime("%Y-%m-%d %H:%M:%S")
        env_vars = ActionTaskEnvironmentVariable.query.filter_by(action_task_id=action_task.id).all()

        # 获取智能体的变量 / Get agent variables
        from app.models import AgentVariable
        agent_vars = AgentVariable.query.filter_by(agent_id=agent.id).all()

        # 添加环境变量到系统提示 / Add environment variables to system prompt
        if env_vars or agent_vars:
            system_prompt += "\n## Current Environment Variables and Personal Variables\n"

            # 添加环境变量 / Add environment variables
            if env_vars:
                system_prompt += "### Task Environment Variables\n"
                for var in env_vars:
                    system_prompt += f"- {var.name}: {var.value} ({var.type})\n"

            # 添加智能体变量 / Add agent variables
            if agent_vars:
                system_prompt += "### Your Personal Variables\n"
                for var in agent_vars:
                    # 只添加公开变量或自己的私有变量 / Only add public variables or your own private variables
                    if var.is_public or var.agent_id == agent.id:
                        system_prompt += f"- {var.name}({var.type}): {var.value}\n"
        system_prompt += "</agentAndTaskVariables>"

    # 添加规则集信息 / Add rule set information
    rule_set = None
    rules = []
    if action_task.rule_set_id:
        rule_set = RuleSet.query.get(action_task.rule_set_id)
        if rule_set:
            # 获取规则集中的所有规则 / Get all rules in the rule set
            from app.models import RuleSetRule
            rule_set_rules = RuleSetRule.query.filter_by(rule_set_id=rule_set.id).all()
            if rule_set_rules:
                rule_ids = [rsr.rule_id for rsr in rule_set_rules]
                rules = Rule.query.filter(Rule.id.in_(rule_ids)).all()
                logger.info(f"从规则集 {rule_set.id} 获取到 {len(rules)} 条规则")

    if rule_set:
        # 添加具体规则 / Add specific rules
        if rules:
            rulesPrompts = ""
            system_prompt += "\n"
            for rule in rules:
                rulesPrompts += f"- {rule.name}\n{rule.description}\n"

        system_prompt += f"""<rules>
## In this action task, you must follow these rules:
### Rule Set Name
{rule_set.name}

### Rule Set Description
{rule_set.description}

### Rule Set Rules
{rulesPrompts}
</rules>
"""
    system_prompt += "</actionTask>"
    system_prompt += "</actionSpace>"

    # 初始化能力字典 / Initialize capabilities dictionary
    agent_capabilities = dict() # 存储在字典中，包括能力和描述，方便后续查删操作，key为能力名称，value为能力描述 / Store in dictionary with capability name as key and description as value for easy lookup and deletion

    if agent_role:
        # 查询角色的能力 / Query role capabilities
        role_capability_relations = RoleCapability.query.filter_by(role_id=agent_role.id).all()
        for rc in role_capability_relations:
            capability = Capability.query.get(rc.capability_id)
            if capability:
                agent_capabilities[capability.name]=capability.description

    if "workspace_management" in agent_capabilities:
        del agent_capabilities["workspace_management"]
        workspace_dir = os.getcwd()+f"/agent-workspace"
        system_prompt += f"""<agentWorkspace>
## Project Workspace Capability
You have project workspace capability that allows you to create and manage project files.
*Project workspace is different from variables. Variables are only related to the current session, while your project workspace can be used for multiple future tasks*
Project files are stored in the `{workspace_dir}` directory, organized according to the following structure:

### Project File Structure
```
- {workspace_dir}/ActionTask-{action_task.id}/
- Agent-{agent.id}/
- AgentWorkspace.md
- ProjectShared.md
- ProjectSummary.md
- ProjectIndex.md
```

### Workspace Operations
You can operate your workspace in the following ways:
1. Read files: Use the read_file tool to read workspace files
2. Update files: Use the edit_file tool to edit workspace files
3. Retrieve files: Use the search_files tool to search workspace files
4. Share information: Share information with other agents by editing ProjectShared.md
5. Project summary: Record project conclusions and reflections by editing ProjectSummary.md
6. Do not create new workspace files, only use existing files

### Project Workspace Usage Recommendations
- When users ask you to "remember" something, you can record information by editing project files
- Regularly summarize important information to your AgentWorkspace.md file
- Record daily observations and experiences in your personal workspace file
- View or modify ProjectShared.md to share information and experiences with other agents
- Important discoveries and decisions should be recorded in both personal workspace and shared workspace
- Use ProjectIndex.md to help you quickly find the files you need
- **Important: During and at the end of tasks, promptly update the ProjectSummary.md file to record key decisions, important events, achieved goals, experience summaries, etc. This is very important for task evaluation and future improvement**

Your workspace is your important asset. Please use it wisely to improve your work efficiency and decision-making quality.
"""

        # 读取智能体个人工作空间文件
        agent_workspace_content = ""
        agent_workspace_path = os.path.join(WorkspaceService().workspace_dir, f'ActionTask-{action_task.id}/Agent-{agent.id}/AgentWorkspace.md')
        if os.path.exists(agent_workspace_path):
            try:
                with open(agent_workspace_path, 'r', encoding='utf-8') as f:
                    agent_workspace_content = f.read()
            except Exception as e:
                logger.error(f"读取个人工作空间文件失败: {str(e)}")
                agent_workspace_content = "无法读取工作空间文件"
        else:
            agent_workspace_content = "工作空间文件不存在"



        # 读取共享工作空间文件
        shared_workspace_content = ""
        shared_workspace_path = os.path.join(WorkspaceService().workspace_dir, f'ActionTask-{action_task.id}/ProjectShared.md')
        if os.path.exists(shared_workspace_path):
            try:
                with open(shared_workspace_path, 'r', encoding='utf-8') as f:
                    shared_workspace_content = f.read()
            except Exception as e:
                logger.error(f"读取共享工作空间文件失败: {str(e)}")
                shared_workspace_content = "无法读取共享工作空间文件"
        else:
            shared_workspace_content = "共享工作空间文件不存在"

        # 读取项目总结文件
        project_summary_content = ""
        project_summary_path = os.path.join(WorkspaceService().workspace_dir, f'ActionTask-{action_task.id}/ProjectSummary.md')
        if os.path.exists(project_summary_path):
            try:
                with open(project_summary_path, 'r', encoding='utf-8') as f:
                    project_summary_content = f.read()
            except Exception as e:
                logger.error(f"读取项目总结文件失败: {str(e)}")
                task_conclusion_content = "无法读取任务结论文件"
        else:
            project_summary_content = "项目总结文件不存在"

        system_prompt += f"""<WorkspaceContent>
### Existing Project Content

#### Personal Workspace
**{workspace_dir}/ActionTask-{action_task.id}/Agent-{agent.id}/AgentWorkspace.md**
```
{agent_workspace_content}
```

#### Shared Workspace
**{workspace_dir}/ActionTask-{action_task.id}/ProjectShared.md**
```
{shared_workspace_content}
```

#### Project Summary
**{workspace_dir}/ActionTask-{action_task.id}/ProjectSummary.md**
```
{project_summary_content}
```
<WorkspaceContent></agentWorkspace>
"""

    system_prompt += f"""<agentCapabilities>
# Your Main Capabilities\n
"""

    # 构建能力与工具的映射关系 / Build capability-tool mapping
    capability_tools_map = {}
    if agent_role:
        # 查询角色的能力，获取每个能力关联的工具 / Query role capabilities and get tools associated with each capability
        role_capability_relations = RoleCapability.query.filter_by(role_id=agent_role.id).all()
        for rc in role_capability_relations:
            capability = Capability.query.get(rc.capability_id)
            if capability and capability.tools:
                # 收集该能力关联的所有工具 / Collect all tools associated with this capability
                capability_tool_list = []
                for server_name, server_tools in capability.tools.items():
                    if isinstance(server_tools, list) and server_tools:
                        for tool_name in server_tools:
                            capability_tool_list.append(f"{tool_name}")
                capability_tools_map[capability.name] = capability_tool_list

    # 为每个能力添加描述和配套工具 / Add description and supporting tools for each capability
    for capability_name, capability_description in agent_capabilities.items():
        system_prompt += f"""<capbility>## {capability_name}\n{capability_description}\n"""

        # 添加与该能力配套的工具列表 / Add list of tools supporting this capability
        if capability_name in capability_tools_map and capability_tools_map[capability_name]:
            system_prompt += f"""<capbilityTools>### Supporting Tools\n"""
            for tool_info in capability_tools_map[capability_name]:
                system_prompt += f"- {tool_info}\n"
            system_prompt += "</capbilityTools>"
        system_prompt += "</capbility>"

    system_prompt += "</agentCapabilities>"

    # 检查是否具备知识库访问能力，如果有则添加知识库信息 / Check if has knowledge access capability, if so add knowledge base information
    if "knowledge_access" in agent_capabilities and agent_role:
        # 查询角色绑定的内部知识库 / Query internal knowledge bases bound to the role
        internal_knowledges = db.session.query(
            RoleKnowledge, Knowledge
        ).join(
            Knowledge,
            RoleKnowledge.knowledge_id == Knowledge.id
        ).filter(RoleKnowledge.role_id == agent_role.id).all()

        # 查询角色绑定的外部知识库 / Query external knowledge bases bound to the role
        external_knowledges = db.session.query(
            RoleExternalKnowledge, ExternalKnowledge, ExternalKnowledgeProvider
        ).join(
            ExternalKnowledge,
            RoleExternalKnowledge.external_knowledge_id == ExternalKnowledge.id
        ).join(
            ExternalKnowledgeProvider,
            ExternalKnowledge.provider_id == ExternalKnowledgeProvider.id
        ).filter(
            RoleExternalKnowledge.role_id == agent_role.id,
            ExternalKnowledge.status == 'active',
            ExternalKnowledgeProvider.status == 'active'
        ).all()

        # 如果有绑定的知识库，添加知识库信息到提示词 / If there are bound knowledge bases, add knowledge base information to prompt
        if internal_knowledges or external_knowledges:
            system_prompt += """<knowledgeBases>
# Available Knowledge Bases
You have access to the following knowledge bases through the query_knowledge tool. Use this tool to search for relevant information when needed.

"""

            # 添加内部知识库信息 / Add internal knowledge base information
            if internal_knowledges:
                system_prompt += "## Internal Knowledge Bases\n"
                for _, knowledge in internal_knowledges:
                    system_prompt += f"- **{knowledge.name}**: {knowledge.description or 'No description available'}\n"
                system_prompt += "\n"

            # 添加外部知识库信息 / Add external knowledge base information
            if external_knowledges:
                system_prompt += "## External Knowledge Bases\n"
                for _, ext_knowledge, provider in external_knowledges:
                    system_prompt += f"- **{ext_knowledge.name}** (via {provider.name}): {ext_knowledge.description or 'No description available'}\n"
                system_prompt += "\n"

            system_prompt += """## How to Query Knowledge Bases
Use the `query_knowledge` tool with the following parameters:
- agent_id: Your agent ID (use your ID: {})
- query: Your search query text

The tool will automatically search all knowledge bases bound to your role and return relevant information.
</knowledgeBases>

""".format(agent.id)

    # 如果有工具，添加工具调用格式指导 / If there are tools, add tool usage instructions
    if ("tool_use" in agent_capabilities or "function_calling" in agent_capabilities) and tool_names:
        system_prompt += """<toolUsage>IMPORTANT INSTRUCTIONS:
- Before calling any tool, first output your motivation and reasoning for using that specific tool
- Use the provided tools when appropriate to answer user questions
- Do NOT describe what tools you would use - actually use them after explaining your motivation
- Do NOT output function calls as text - use the proper tool calling mechanism
- If you need clarification for tool parameters, ask the user first
- Only use tools when necessary to fulfill the user's request
"""
        for tool_name in tool_names:
            system_prompt += f"- {tool_name}\n" #: {tool_definitions[tool_names.index(tool_name)]['function']['description']}\n" #暂时只列出工具列表节省上下文 / Temporarily only list tool names to save context
        system_prompt += "</toolUsage>"
    return system_prompt

def format_messages(system_prompt, recent_messages, current_content, human_message):
    """格式化消息为模型可用的格式 / Format messages for model use

    Args:
        system_prompt: 系统提示词 / System prompt
        recent_messages: 最近的消息列表 / Recent messages list
        current_content: 当前消息内容 / Current message content
        human_message: 人类消息对象 / Human message object

    Returns:
        list: 格式化后的消息列表 / Formatted message list
    """
    from app.models import SystemSetting
    import re
    import json
    import uuid

    # 获取是否在上下文中包含思考内容的系统设置 / Get system setting for whether to include thinking content in context
    include_thinking = SystemSetting.get('includeThinkingContentInContext', True)
    logger.debug(f"是否在上下文中包含思考内容: {include_thinking}")

    # 获取是否将工具调用拆分为独立历史消息的系统设置 / Get system setting for whether to split tool calls into separate history messages
    split_tool_calls = SystemSetting.get('splitToolCallsInHistory', True)
    logger.debug(f"是否将工具调用拆分为独立历史消息: {split_tool_calls}")

    model_messages = []

    # 添加系统提示词 / Add system prompt
    model_messages.append({
        "role": "system",
        "content": system_prompt
    })

    # 添加历史消息 / Add historical messages
    if recent_messages:
        for msg in recent_messages:
            # 确保跳过最新的用户消息(内容匹配)，即使human_message未创建到数据库 / Skip the latest user message (content match) even if human_message is not created in database
            if msg.role == "human" and msg.content == current_content:
                continue

            # 根据消息角色添加到不同的消息中 / Add to different messages based on message role
            if msg.role == "human":
                model_messages.append({
                    "role": "user",
                    "content": msg.content
                })
            elif (msg.role == "agent" or msg.role == "supervisor") and msg.agent_id:
                # 处理智能体消息，检查是否包含工具调用 / Handle agent messages and check for tool calls
                expanded_messages = _expand_assistant_message_with_tool_calls(msg, include_thinking, split_tool_calls)
                model_messages.extend(expanded_messages)


    # 添加当前用户消息 / Add current user message
    model_messages.append({
        "role": "user",
        "content": current_content
    })

    return model_messages


def _expand_assistant_message_with_tool_calls(msg, include_thinking=False, split_tool_calls=True):
    """
    将包含工具调用的assistant消息扩展为独立的消息列表，保持工具调用在内容中的原始顺序

    Args:
        msg: 数据库中的消息对象
        include_thinking: 是否包含思考内容
        split_tool_calls: 是否将工具调用拆分为独立的历史消息

    Returns:
        list: 扩展后的消息列表，按原始顺序包含assistant、tool等角色的消息
    """
    import re
    import json
    import uuid

    # 处理智能体消息中的思考内容 / Handle thinking content in agent messages
    message_content = msg.content

    # 检查消息是否包含思考内容 / Check if message contains thinking content
    has_thinking_content = '<thinking>' in message_content or '<think>' in message_content

    # 如果不包含思考内容，则移除<thinking>标签及其内容 / If not including thinking content, remove <thinking> tags and their content
    if not include_thinking and has_thinking_content:
        # 记录原始内容长度 / Record original content length
        original_length = len(message_content)

        # 查找所有<thinking>和<think>标签内容，用于日志记录 / Find all <thinking> and <think> tag content for logging
        thinking_contents = re.findall(r'<thinking>(.*?)</thinking>', message_content, flags=re.DOTALL)
        think_contents = re.findall(r'<think>(.*?)</think>', message_content, flags=re.DOTALL)
        all_thinking_contents = thinking_contents + think_contents
        if all_thinking_contents:
            logger.debug(f"找到 {len(thinking_contents)} 个<thinking>标签内容和 {len(think_contents)} 个<think>标签内容")
            for i, content in enumerate(all_thinking_contents):
                tag_type = "<thinking>" if i < len(thinking_contents) else "<think>"
                logger.debug(f"{tag_type}标签 #{i+1} 内容前20个字符: {content[:20]}...")

        # 移除<thinking>...</thinking>标签及其内容，使用非贪婪匹配确保只匹配每对标签之间的内容 / Remove <thinking>...</thinking> tags and content using non-greedy matching
        message_content = re.sub(r'<thinking>.*?</thinking>', '', message_content, flags=re.DOTALL)

        # 移除<think>...</think>标签及其内容，同样使用非贪婪匹配 / Remove <think>...</think> tags and content using non-greedy matching
        message_content = re.sub(r'<think>.*?</think>', '', message_content, flags=re.DOTALL)

        # 记录移除后的内容长度 / Record content length after removal
        new_length = len(message_content)
        logger.info(f"从消息中移除思考内容: 原始长度={original_length}, 新长度={new_length}, 移除了{original_length - new_length}个字符")

    # 获取智能体信息 / Get agent information
    agent_name = None
    role_name = None

    # 尝试从消息中获取智能体信息 / Try to get agent information from message
    if msg.agent_id:
        from app.models import Agent, Role
        agent = Agent.query.get(msg.agent_id)
        if agent:
            agent_name = agent.name
            if hasattr(agent, 'role_id') and agent.role_id:
                role = Role.query.get(agent.role_id)
                if role:
                    role_name = role.name

    # 按顺序解析消息内容，保持工具调用的原始位置 / Parse message content in order, maintaining original tool call positions
    message_segments = _parse_message_segments_with_tool_calls(message_content)

    expanded_messages = []

    # 检查是否包含工具调用以及是否需要扩展 / Check if contains tool calls and whether to expand
    has_tool_calls = any(segment['type'] == 'tool_result' for segment in message_segments)

    # 如果有工具调用且配置为拆分工具调用，按顺序创建消息序列 / If there are tool calls and configured to split them, create message sequence in order
    if has_tool_calls and split_tool_calls:
        logger.debug(f"在消息中发现工具调用，将按原始顺序扩展为独立消息")

        # 按顺序处理每个段落 / Process each segment in order
        current_assistant_content = ""

        for segment in message_segments:
            if segment['type'] == 'content':
                # 累积文本内容 / Accumulate text content
                current_assistant_content += segment['content']
            elif segment['type'] == 'tool_result':
                # 遇到工具调用结果，需要先创建包含工具调用的assistant消息
                # 然后立即创建tool消息

                # 创建assistant消息（包含当前内容和工具调用）
                assistant_message = {
                    "role": "assistant",
                    "content": current_assistant_content.strip(),
                    "tool_calls": [segment['tool_call']]
                }

                # 添加智能体标识信息（只在第一个assistant消息中添加）
                if not expanded_messages and agent_name and role_name:
                    role_indicator = "Supervisor" if msg.role == "supervisor" else "Agent"
                    assistant_message["content"] = f"""<!--The following is a reply from {agent_name}[{role_name}][{role_indicator}][ID: {agent.id}]-->
{assistant_message["content"]}"""

                expanded_messages.append(assistant_message)

                # 立即添加tool消息
                tool_message = {
                    "role": "tool",
                    "tool_call_id": segment['tool_call']['id'],
                    "content": segment['content']
                }
                expanded_messages.append(tool_message)

                # 重置当前内容
                current_assistant_content = ""

        # 处理最后剩余的内容
        if current_assistant_content.strip():
            assistant_message = {
                "role": "assistant",
                "content": current_assistant_content.strip()
            }

            # 添加智能体标识信息（只在第一个assistant消息中添加）
            if not expanded_messages and agent_name and role_name:
                role_indicator = "Supervisor" if msg.role == "supervisor" else "Agent"
                assistant_message["content"] = f"""<!--The following is a reply from {agent_name}[{role_name}][{role_indicator}][ID: {agent.id}]-->
{assistant_message["content"]}"""

            expanded_messages.append(assistant_message)

        logger.debug(f"扩展消息完成：共生成 {len(expanded_messages)} 个消息")
    elif has_tool_calls and not split_tool_calls:
        # 有工具调用但配置为不拆分，保留完整内容包括工具调用结果 / Has tool calls but configured not to split, keep complete content including tool results
        logger.debug(f"在消息中发现工具调用，但配置为不拆分，将保留完整内容")

        # 重建完整内容，包括文本和工具调用结果 / Rebuild complete content including text and tool results
        complete_content = ""
        for segment in message_segments:
            if segment['type'] == 'content':
                complete_content += segment['content']
            elif segment['type'] == 'tool_result':
                # 将工具调用结果以文本形式添加到内容中 / Add tool call results as text to content
                tool_name = segment['tool_call']['function']['name']
                tool_result = segment['content']
                complete_content += f"\n[工具调用结果 - {tool_name}]: {tool_result}\n"

        # 清理多余的空行 / Clean up extra empty lines
        complete_content = re.sub(r'\n\s*\n\s*\n', '\n\n', complete_content)
        complete_content = complete_content.strip()

        # 添加智能体标识信息 / Add agent identification info
        if agent_name and role_name:
            role_indicator = "Supervisor" if msg.role == "supervisor" else "Agent"
            complete_content = f"""<!--The following is a reply from {agent_name}[{role_name}][{role_indicator}][ID: {agent.id}]-->
{complete_content}"""

        expanded_messages.append({
            "role": "assistant",
            "content": complete_content
        })

        logger.debug(f"保留完整内容完成，生成1个assistant消息")
    else:
        # 没有工具调用，按原来的方式处理 / No tool calls, process as before
        if agent_name and role_name:
            role_indicator = "Supervisor" if msg.role == "supervisor" else "Agent"
            message_content = f"""<!--The following is a reply from {agent_name}[{role_name}][{role_indicator}][ID: {agent.id}]-->
{message_content}"""

        expanded_messages.append({
            "role": "assistant",
            "content": message_content
        })

    return expanded_messages


def _parse_message_segments_with_tool_calls(content):
    """
    按顺序解析消息内容，将其分割为文本段落和工具调用结果段落

    Args:
        content: 消息内容字符串

    Returns:
        list: 段落列表，每个段落包含type和content字段
            - type: 'content' 或 'tool_result'
            - content: 段落内容
            - tool_call: 工具调用信息（仅当type为'tool_result'时）
    """
    import re
    import json
    import uuid

    segments = []

    try:
        # 1. 查找所有JSON格式的工具调用结果及其位置
        # 改进正则表达式，使用更健壮的匹配方式
        tool_result_pattern = r'\{"content":\s*null,\s*"meta":\s*\{.*?"type":\s*"toolResult".*?\}\}'

        # 使用finditer来获取匹配位置
        matches = list(re.finditer(tool_result_pattern, content, re.DOTALL))

        # 添加调试日志
        logger.debug(f"[工具调用解析] 消息内容长度: {len(content)}")
        logger.debug(f"[工具调用解析] 找到工具调用结果数量: {len(matches)}")

        # 如果没有匹配但内容中包含可能的工具调用结果，记录警告
        if not matches and ('toolResult' in content or 'tool_call_id' in content):
            logger.warning(f"[工具调用解析] 消息中可能包含工具调用结果但未被正则表达式匹配")
            logger.warning(f"[工具调用解析] 消息内容前500字符: {content[:500]}")

        if not matches:
            # 没有工具调用结果，整个内容作为一个文本段落
            segments.append({
                'type': 'content',
                'content': content
            })
            return segments

        # 按位置顺序处理内容
        last_end = 0

        for match in matches:
            start_pos = match.start()
            end_pos = match.end()

            # 添加工具调用结果之前的文本内容
            if start_pos > last_end:
                text_content = content[last_end:start_pos]
                if text_content.strip():  # 只添加非空内容
                    segments.append({
                        'type': 'content',
                        'content': text_content
                    })

            # 解析工具调用结果
            try:
                result_data = json.loads(match.group())
                meta = result_data.get('meta', {})
                if meta.get('type') == 'toolResult' and meta.get('role') == 'tool':
                    tool_call_id = meta.get('tool_call_id', str(uuid.uuid4()))
                    tool_name = meta.get('tool_name', 'unknown_tool')
                    tool_content = meta.get('content', '')
                    tool_parameter = meta.get('tool_parameter', '{}')

                    # 创建工具调用对象
                    tool_call = {
                        "id": tool_call_id,
                        "type": "function",
                        "function": {
                            "name": tool_name,
                            "arguments": tool_parameter
                        }
                    }

                    # 添加工具调用结果段落
                    segments.append({
                        'type': 'tool_result',
                        'content': tool_content,
                        'tool_call': tool_call
                    })

                    logger.debug(f"解析到工具调用结果段落: {tool_name}, 位置: {start_pos}-{end_pos}")

            except json.JSONDecodeError as e:
                logger.warning(f"解析工具调用结果JSON失败: {e}")
                # 解析失败时，将其作为普通文本处理
                segments.append({
                    'type': 'content',
                    'content': match.group()
                })

            last_end = end_pos

        # 添加最后剩余的文本内容
        if last_end < len(content):
            text_content = content[last_end:]
            if text_content.strip():  # 只添加非空内容
                segments.append({
                    'type': 'content',
                    'content': text_content
                })

        logger.debug(f"消息内容分割为 {len(segments)} 个段落")

    except Exception as e:
        logger.error(f"解析消息段落时出错: {e}")
        # 出错时返回整个内容作为文本段落
        segments = [{
            'type': 'content',
            'content': content
        }]

    return segments
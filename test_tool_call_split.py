#!/usr/bin/env python3
"""
测试工具调用拆分功能的脚本
"""

import json
import re
import uuid

class MockMessage:
    """模拟Message对象"""
    def __init__(self, content, role='agent', agent_id=1):
        self.content = content
        self.role = role
        self.agent_id = agent_id

def test_tool_call_parsing():
    """测试工具调用解析"""
    print("=== 测试工具调用解析 ===")
    
    # 模拟包含工具调用结果的消息内容
    tool_result_json = {
        "content": None,
        "meta": {
            "type": "toolResult",
            "role": "tool",
            "content": "测试工具调用结果",
            "tool_call_id": "test_call_123",
            "tool_name": "test_tool",
            "tool_parameter": '{"param": "value"}',
            "status": "success"
        }
    }
    
    message_content = f"这是一些文本内容。{json.dumps(tool_result_json, ensure_ascii=False)}更多文本内容。"
    
    print(f"原始消息内容: {message_content}")
    print()
    
    # 解析消息段落
    segments = _parse_message_segments_with_tool_calls(message_content)
    print(f"解析出的段落数量: {len(segments)}")
    
    for i, segment in enumerate(segments):
        print(f"段落 {i+1}:")
        print(f"  类型: {segment['type']}")
        print(f"  内容: {segment['content'][:100]}{'...' if len(segment['content']) > 100 else ''}")
        if segment['type'] == 'tool_result':
            print(f"  工具调用ID: {segment['tool_call']['id']}")
            print(f"  工具名称: {segment['tool_call']['function']['name']}")
        print()

def test_message_expansion():
    """测试消息扩展"""
    print("=== 测试消息扩展 ===")
    
    # 创建包含工具调用结果的模拟消息
    tool_result_json = {
        "content": None,
        "meta": {
            "type": "toolResult",
            "role": "tool",
            "content": "工具执行成功，返回结果：Hello World",
            "tool_call_id": "call_abc123",
            "tool_name": "echo_tool",
            "tool_parameter": '{"message": "Hello World"}',
            "status": "success"
        }
    }
    
    message_content = f"我将调用工具来处理您的请求。{json.dumps(tool_result_json, ensure_ascii=False)}工具调用完成，结果已返回。"
    
    mock_msg = MockMessage(message_content)
    
    print(f"原始消息内容: {message_content}")
    print()
    
    # 测试启用拆分的情况
    print("--- 启用工具调用拆分 ---")
    expanded_messages_split = _expand_assistant_message_with_tool_calls(
        mock_msg, 
        include_thinking=False, 
        split_tool_calls=True
    )
    
    print(f"扩展后消息数量: {len(expanded_messages_split)}")
    for i, msg in enumerate(expanded_messages_split):
        print(f"消息 {i+1}:")
        print(f"  角色: {msg['role']}")
        print(f"  内容: {msg.get('content', 'N/A')[:100]}{'...' if msg.get('content') and len(msg.get('content', '')) > 100 else ''}")
        if 'tool_calls' in msg:
            print(f"  工具调用: {len(msg['tool_calls'])} 个")
        if 'tool_call_id' in msg:
            print(f"  工具调用ID: {msg['tool_call_id']}")
        print()
    
    # 测试禁用拆分的情况
    print("--- 禁用工具调用拆分 ---")
    expanded_messages_no_split = _expand_assistant_message_with_tool_calls(
        mock_msg, 
        include_thinking=False, 
        split_tool_calls=False
    )
    
    print(f"扩展后消息数量: {len(expanded_messages_no_split)}")
    for i, msg in enumerate(expanded_messages_no_split):
        print(f"消息 {i+1}:")
        print(f"  角色: {msg['role']}")
        print(f"  内容: {msg.get('content', 'N/A')[:200]}{'...' if msg.get('content') and len(msg.get('content', '')) > 200 else ''}")
        print()

if __name__ == "__main__":
    test_tool_call_parsing()
    test_message_expansion()

#!/usr/bin/env python3
"""
调试工具调用解析问题
"""

import json
import re

def test_complex_tool_result():
    """测试复杂的工具调用结果格式"""
    
    print("=== 测试复杂工具调用结果的正则表达式匹配 ===")
    
    # 模拟一个复杂的工具调用结果，包含嵌套的JSON结构
    complex_result = {
        "content": None,
        "meta": {
            "type": "toolResult",
            "role": "tool",
            "content": '{"status": "success", "data": {"items": [{"id": 1, "name": "test"}]}, "message": "操作完成"}',
            "tool_call_id": "call_abc123",
            "tool_name": "complex_tool",
            "tool_parameter": '{"param1": "value1", "nested": {"key": "value"}}',
            "status": "success"
        }
    }
    
    json_str = json.dumps(complex_result, ensure_ascii=False)
    print("复杂工具调用结果JSON:")
    print(json_str)
    print()
    
    # 当前的正则表达式
    current_pattern = r'\{"content":\s*null,\s*"meta":\s*\{[^}]*?"type":\s*"toolResult".*?\}\}'
    
    print("当前正则表达式:")
    print(current_pattern)
    print()
    
    # 测试匹配
    matches = re.findall(current_pattern, json_str, re.DOTALL)
    print(f"当前正则表达式匹配数量: {len(matches)}")
    
    if matches:
        print("匹配成功!")
        for i, match in enumerate(matches):
            print(f"匹配 {i+1}: {match[:200]}...")
    else:
        print("匹配失败!")
        
        # 分析问题
        print("\n问题分析:")
        print("1. [^}]*? 模式遇到嵌套JSON时会提前停止")
        print("2. 当meta对象包含复杂嵌套结构时，正则表达式无法正确匹配")
        
        # 尝试改进的正则表达式
        print("\n尝试改进的正则表达式:")
        
        # 方法1：使用更宽松的匹配
        improved_pattern1 = r'\{"content":\s*null,\s*"meta":\s*\{.*?"type":\s*"toolResult".*?\}\}'
        print(f"方法1: {improved_pattern1}")
        
        matches1 = re.findall(improved_pattern1, json_str, re.DOTALL)
        print(f"方法1匹配数量: {len(matches1)}")
        
        if matches1:
            print("方法1匹配成功!")
        else:
            print("方法1匹配失败!")
            
        # 方法2：使用平衡括号匹配
        print("\n方法2：手动解析JSON结构")
        try:
            # 查找开始位置
            start_pattern = r'\{"content":\s*null,\s*"meta":\s*\{'
            start_match = re.search(start_pattern, json_str)
            
            if start_match:
                start_pos = start_match.start()
                print(f"找到开始位置: {start_pos}")
                
                # 从开始位置手动匹配平衡的大括号
                brace_count = 0
                end_pos = start_pos
                
                for i, char in enumerate(json_str[start_pos:], start_pos):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            end_pos = i + 1
                            break
                
                if brace_count == 0:
                    matched_json = json_str[start_pos:end_pos]
                    print("方法2匹配成功!")
                    print(f"匹配内容: {matched_json[:200]}...")
                    
                    # 验证是否包含toolResult
                    if '"type":"toolResult"' in matched_json or '"type": "toolResult"' in matched_json:
                        print("确认包含toolResult类型!")
                    else:
                        print("不包含toolResult类型")
                else:
                    print("方法2匹配失败：括号不平衡")
            else:
                print("方法2匹配失败：找不到开始位置")
                
        except Exception as e:
            print(f"方法2出错: {e}")

def test_real_world_scenario():
    """测试真实世界的场景"""
    
    print("\n=== 测试真实世界场景 ===")
    
    # 模拟一个包含多个工具调用结果的消息内容
    tool_result1 = {
        "content": None,
        "meta": {
            "type": "toolResult",
            "role": "tool",
            "content": "简单的工具调用结果",
            "tool_call_id": "call_1",
            "tool_name": "simple_tool",
            "tool_parameter": '{}',
            "status": "success"
        }
    }
    
    tool_result2 = {
        "content": None,
        "meta": {
            "type": "toolResult",
            "role": "tool",
            "content": '{"complex": {"nested": {"data": [1, 2, 3]}, "status": "ok"}}',
            "tool_call_id": "call_2",
            "tool_name": "complex_tool",
            "tool_parameter": '{"input": {"type": "json", "data": {"key": "value"}}}',
            "status": "success"
        }
    }
    
    # 构建包含多个工具调用结果的消息内容
    message_content = f"""我将执行一些工具调用来帮助您。

首先执行简单工具：
{json.dumps(tool_result1, ensure_ascii=False)}

然后执行复杂工具：
{json.dumps(tool_result2, ensure_ascii=False)}

所有工具调用已完成。"""
    
    print("真实世界消息内容:")
    print(message_content)
    print()
    
    # 测试当前正则表达式
    current_pattern = r'\{"content":\s*null,\s*"meta":\s*\{[^}]*?"type":\s*"toolResult".*?\}\}'
    matches = re.findall(current_pattern, message_content, re.DOTALL)
    
    print(f"当前正则表达式匹配数量: {len(matches)}")
    print("期望匹配数量: 2")
    
    if len(matches) == 2:
        print("✅ 匹配成功!")
    else:
        print("❌ 匹配失败!")
        print("这可能就是SSE类型工具调用不被拆分的原因!")

if __name__ == "__main__":
    test_complex_tool_result()
    test_real_world_scenario()
